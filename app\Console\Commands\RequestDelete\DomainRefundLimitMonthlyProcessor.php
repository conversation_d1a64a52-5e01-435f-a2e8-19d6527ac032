<?php

namespace App\Console\Commands\RequestDelete;

use App\Modules\RequestDelete\Services\DomainRegistrationRefundLimitService;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Console\Command;

class DomainRefundLimitMonthlyProcessor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:domain-refund-limit-monthly-processor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monthly processor: Reset counters and calculate new limits based on previous month registrations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainRefundLimitMonthlyProcessor: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('DomainRefundLimitMonthlyProcessor: Running monthly processing...');

        $service = DomainRegistrationRefundLimitService::instance();

        // Step 1: Reset counters to 0 (start fresh for new month)
        app(AuthLogger::class)->info('DomainRefundLimitMonthlyProcessor: Resetting counters...');
        $service->resetMonthlyCounters();

        // Step 2: Calculate and update new limits based on previous month's registrations
        app(AuthLogger::class)->info('DomainRefundLimitMonthlyProcessor: Calculating new limits...');
        $service->calculateAndUpdateMonthlyLimits();

        app(AuthLogger::class)->info('DomainRefundLimitMonthlyProcessor: Monthly processing completed successfully');
    }
}
