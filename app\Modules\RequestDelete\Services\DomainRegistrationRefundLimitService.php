<?php

namespace App\Modules\RequestDelete\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainRegistrationRefundLimitService
{
    public static function instance(): self
    {
        return new self();
    }

    /**
     * Check if a domain deletion request can be processed based on refund limits
     * Only applies to newly registered domains (0-5 days old)
     */
    public function canProcessDeletion(int $registeredDomainId): bool
    {
        // Check if domain is newly registered (0-5 days)
        if (!$this->isNewlyRegisteredDomain($registeredDomainId)) {
            return true; // No limit for domains older than 5 days
        }

        $limitRecord = $this->getOrCreateLimitRecord($registeredDomainId);
        
        return $limitRecord->times_triggered < $limitRecord->limit;
    }

    /**
     * Increment the counter when a deletion request is approved
     */
    public function incrementCounter(int $registeredDomainId): void
    {
        // Only increment for newly registered domains
        if (!$this->isNewlyRegisteredDomain($registeredDomainId)) {
            return;
        }

        $limitRecord = $this->getOrCreateLimitRecord($registeredDomainId);
        
        DB::client()->table('domain_registration_refund_limit')
            ->where('registered_domain_id', $registeredDomainId)
            ->increment('times_triggered');

        app(AuthLogger::class)->info("Domain refund limit counter incremented for registered_domain_id: {$registeredDomainId}");
    }

    /**
     * Decrement the counter when an approved request is cancelled
     */
    public function decrementCounter(int $registeredDomainId): void
    {
        // Only decrement for newly registered domains
        if (!$this->isNewlyRegisteredDomain($registeredDomainId)) {
            return;
        }

        DB::client()->table('domain_registration_refund_limit')
            ->where('registered_domain_id', $registeredDomainId)
            ->where('times_triggered', '>', 0)
            ->decrement('times_triggered');

        app(AuthLogger::class)->info("Domain refund limit counter decremented for registered_domain_id: {$registeredDomainId}");
    }

    /**
     * Check if domain is newly registered (0-5 days old)
     */
    private function isNewlyRegisteredDomain(int $registeredDomainId): bool
    {
        $domain = DB::client()->table('registered_domains')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->where('registered_domains.id', $registeredDomainId)
            ->select('domains.created_at')
            ->first();

        if (!$domain) {
            return false;
        }

        $domainCreatedAt = Carbon::parse($domain->created_at);
        $daysSinceCreation = $domainCreatedAt->diffInDays(Carbon::now());

        return $daysSinceCreation <= 5;
    }

    /**
     * Get or create limit record for a registered domain
     */
    private function getOrCreateLimitRecord(int $registeredDomainId): object
    {
        $existingRecord = DB::client()->table('domain_registration_refund_limit')
            ->where('registered_domain_id', $registeredDomainId)
            ->first();

        if ($existingRecord) {
            return $existingRecord;
        }

        // Create new record with default values
        DB::client()->table('domain_registration_refund_limit')->insert([
            'registered_domain_id' => $registeredDomainId,
            'limit' => 50,
            'action' => true,
            'period_type' => 'monthly',
            'times_triggered' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return DB::client()->table('domain_registration_refund_limit')
            ->where('registered_domain_id', $registeredDomainId)
            ->first();
    }

    /**
     * Calculate monthly average registrations and update limits
     * Called by monthly console command
     */
    public function calculateAndUpdateMonthlyLimits(): void
    {
        $previousMonth = Carbon::now()->subMonth();
        $startOfMonth = $previousMonth->startOfMonth();
        $endOfMonth = $previousMonth->endOfMonth();

        // Get total registrations for the previous month
        $totalRegistrations = DB::client()->table('domains')
            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->count();

        // Calculate 10% of total registrations
        $calculatedLimit = (int) ceil($totalRegistrations * 0.10);
        
        // Use default minimum of 50 if calculated limit is lower
        $newLimit = max($calculatedLimit, 50);

        // Update all existing records with the new limit
        $updatedCount = DB::client()->table('domain_registration_refund_limit')
            ->update([
                'limit' => $newLimit,
                'updated_at' => now(),
            ]);

        app(AuthLogger::class)->info("Monthly limit calculation completed. Total registrations: {$totalRegistrations}, New limit: {$newLimit}, Records updated: {$updatedCount}");
    }

    /**
     * Reset all counters to 0 at the beginning of each month
     * Called by monthly reset console command
     */
    public function resetMonthlyCounters(): void
    {
        $updatedCount = DB::client()->table('domain_registration_refund_limit')
            ->update([
                'times_triggered' => 0,
                'updated_at' => now(),
            ]);

        app(AuthLogger::class)->info("Monthly counter reset completed. Records reset: {$updatedCount}");
    }

    /**
     * Get current limit and usage for a registered domain
     */
    public function getLimitInfo(int $registeredDomainId): array
    {
        if (!$this->isNewlyRegisteredDomain($registeredDomainId)) {
            return [
                'applies' => false,
                'reason' => 'Domain is older than 5 days'
            ];
        }

        $limitRecord = $this->getOrCreateLimitRecord($registeredDomainId);

        return [
            'applies' => true,
            'limit' => $limitRecord->limit,
            'times_triggered' => $limitRecord->times_triggered,
            'remaining' => $limitRecord->limit - $limitRecord->times_triggered,
            'can_process' => $limitRecord->times_triggered < $limitRecord->limit
        ];
    }
}
