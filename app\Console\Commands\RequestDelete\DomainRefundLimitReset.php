<?php

namespace App\Console\Commands\RequestDelete;

use App\Modules\RequestDelete\Services\DomainRegistrationRefundLimitService;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Console\Command;

class DomainRefundLimitReset extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:domain-refund-limit-reset';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset monthly domain refund limit counters to 0';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainRefundLimitReset: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('DomainRefundLimitReset: Running...');

        DomainRegistrationRefundLimitService::instance()->resetMonthlyCounters();

        app(AuthLogger::class)->info('DomainRefundLimitReset: Done');
    }
}
