<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_registration_refund_limit', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('registered_domain_id');
            $table->integer('limit')->default(50);
            $table->boolean('action')->default(true);
            $table->string('period_type')->default('monthly');
            $table->integer('times_triggered')->default(0);
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('registered_domain_id')->references('id')->on('registered_domains')->onDelete('cascade');

            // Index for better performance
            $table->index('registered_domain_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_registration_refund_limit');
    }
};
