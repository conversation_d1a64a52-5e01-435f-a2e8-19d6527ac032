<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_registration_refund_limit', function (Blueprint $table) {
            $table->id();
            $table->integer('limit')->default(50);
            $table->boolean('action')->default(true);
            $table->string('period_type')->default('monthly');
            $table->integer('times_triggered')->default(0);
            $table->timestamps();
        });

        // Insert default configuration record
        DB::table('domain_registration_refund_limit')->insert([
            'limit' => 50,
            'action' => true,
            'period_type' => 'monthly',
            'times_triggered' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_registration_refund_limit');
    }
};
