<?php

namespace App\Modules\DomainRedemption\Services;

use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class DatabaseQueryService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance()
    {
        $DatabaseQueryService = new self;

        return $DatabaseQueryService;
    }

    public function get($request)
    {
        $pageLimit = $request->input('limit', self::$pageLimit);
        $builder = self::getDomain();
        self::whenHasUser($builder, $request);
        self::whenHasDomain($builder, $request);
        self::whenHasEmail($builder, $request);
        self::whenHasDeletedBy($builder, $request);
        self::whenHas<PERSON>rderby($builder, $request);
        $builder = $builder->paginate($pageLimit)->withQueryString();
        return [
            ...CursorPaginate::cursor($builder, self::paramToURI($request)),
            "search" => $request->search ?? ""
        ];
    }
    
    // PRIVATE Functions

    private function getDomain(): Builder
    {
        return DB::client()->table('pending_domain_deletions')
            ->join('registered_domains', 'registered_domains.id', '=', 'pending_domain_deletions.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->whereNotNull('pending_domain_deletions.deleted_at')
            ->select(self::getSelectFields());
    }

    private function getSelectFields(): array
    {
        return [
            'pending_domain_deletions.id as delete_id',
            'pending_domain_deletions.deleted_by',
            'pending_domain_deletions.deleted_at as domain_deleted_at',
            'pending_domain_deletions.created_at as scraped_at',
            'domains.*',
            'registered_domains.id as registered_domain_id',
            'users.email as user_email',
            'users.first_name',
            'users.last_name',
        ];
    }

    private function whenHasOrderby(Builder &$builder, $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'dateScraped':
                        $query->orderBy('delete_id', $orderby[1]);
                        break;
                    case 'name':
                    case 'domain':
                        $query->orderBy('name', $orderby[1]);
                        break;
                    case 'dateDeleted':
                        $query->orderBy('pending_domain_deletions.deleted_at', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('delete_id', 'desc');
                }
            } else {
                $query->orderBy('delete_id', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $query) {
                $query->orderBy('delete_id', 'desc');
            });
    }

    private static function whenHasUser(&$builder, $request)
    {
        $builder->when($request->has('user'), function (Builder $query) use ($request) {
            $user = $request->user;
            $query->where(DB::raw("first_name || ' ' || last_name"), 'ILIKE', "$user%");
        });
    }

    private static function whenHasDomain(&$builder, $request)
    {
        $builder->when(($request->has('domain') || $request->has('search')), function (Builder $query) use ($request) {
            $domain = $request->domain ?? $request->search;
            $query->where('name', 'ilike', "$domain%");
        });
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', "$email%");
        });
    }

    private static function whenHasDeletedBy(&$builder, $request)
    {
        $builder->when($request->has('deletedBy'), function (Builder $query) use ($request) {
            $deletedBy = $request->deletedBy;
            $query->where('deleted_by', 'ilike', "$deletedBy%");
        });
    }

    private function paramToURI($request): array
    {
        $param = [];

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        return $param;
    }
}
