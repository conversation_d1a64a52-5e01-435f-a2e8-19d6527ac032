<?php

namespace App\Console\Commands\RequestDelete;

use App\Modules\RequestDelete\Services\DomainRegistrationRefundLimitService;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;
use Illuminate\Console\Command;

class DomainRefundLimitCalculator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:domain-refund-limit-calculator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate monthly domain refund limits based on 10% of previous month registrations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainRefundLimitCalculator: '.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            echo($e->getMessage());
            throw new Exception($errorMsg);
        }
    }

    public function evaluate()
    {
        app(AuthLogger::class)->info('DomainRefundLimitCalculator: Running...');

        DomainRegistrationRefundLimitService::instance()->calculateAndUpdateMonthlyLimits();

        app(AuthLogger::class)->info('DomainRefundLimitCalculator: Done');
    }
}
